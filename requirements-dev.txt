# Include all requirements to run the bot.
-r requirements.txt
-r requirements-plot.txt
-r requirements-hyperopt.txt
-r requirements-freqai.txt
-r requirements-freqai-rl.txt
-r docs/requirements-docs.txt

ruff==0.12.9
mypy==1.17.1
pre-commit==4.3.0
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-cov==6.2.1
pytest-mock==3.14.1
pytest-random-order==1.2.0
pytest-timeout==2.4.0
pytest-xdist==3.8.0
isort==6.0.1
# For datetime mocking
time-machine==2.17.0

# Convert jupyter notebooks to markdown documents
nbconvert==7.16.6

# mypy types
scipy-stubs==********  # keep in sync with `scipy` in `requirements-hyperopt.txt`
types-cachetools==6.1.0.20250717
types-filelock==3.2.7
types-requests==2.32.4.20250809
types-tabulate==0.9.0.20241207
types-python-dateutil==2.9.0.20250809
