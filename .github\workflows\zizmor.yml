name: GitHub Actions Security Analysis with zizmor 🌈

on:
  push:
    branches:
      - develop
      - stable
  pull_request:
    branches:
      - develop
      - stable

permissions: {}

jobs:
  zizmor:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      # contents: read # only needed for private repos
      # actions: read # only needed for private repos
    steps:
      - name: Checkout repository
        uses: actions/checkout@ff7abcd0c3c05ccf6adc123a8cd1fd4fb30fb493 # v4.2.2
        with:
          persist-credentials: false

      - name: Run zizmor 🌈
        uses: zizmorcore/zizmor-action@5ca5fc7a4779c5263a3ffa0e1f693009994446d1 # v0.1.2
